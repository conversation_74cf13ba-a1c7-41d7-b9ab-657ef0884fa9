<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>立即TTS测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .processing { background-color: #fff3cd; color: #856404; }
        .input-group {
            margin: 20px 0;
        }
        input[type="text"] {
            width: 70%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-text { background-color: #e3f2fd; }
        .log-tts { background-color: #f3e5f5; }
        .log-audio { background-color: #e8f5e8; }
        .log-error { background-color: #ffebee; color: #c62828; }
        .log-info { background-color: #fff3e0; }
        .audio-controls {
            margin: 20px 0;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .metric {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 立即TTS测试</h1>
        <p>测试真正的流式TTS：每收到AI chunk立即进行TTS合成</p>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="input-group">
            <input type="text" id="messageInput" placeholder="输入测试消息..." value="">
            <button id="sendBtn" onclick="sendMessage()" disabled>发送</button>
            <button id="stopBtn" onclick="stopAudio()" disabled>停止音频</button>
        </div>
        
        <div class="audio-controls">
            <button onclick="clearLog()">清空日志</button>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="toggleAutoPlay()">切换自动播放</button>
        </div>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="chunkCount">0</div>
                <div class="metric-label">处理的Chunk数</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="audioCount">0</div>
                <div class="metric-label">音频片段数</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="avgLatency">0ms</div>
                <div class="metric-label">平均延迟</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="totalTime">0ms</div>
                <div class="metric-label">总处理时间</div>
            </div>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let autoPlay = true;
        let audioContext = null;
        let audioQueue = [];
        let isPlaying = false;
        
        // 统计数据
        let stats = {
            chunkCount: 0,
            audioCount: 0,
            latencies: [],
            startTime: null
        };

        // 初始化
        window.onload = function() {
            initAudioContext();
            connect();
        };

        function initAudioContext() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                log('音频上下文初始化成功', 'info');
            } catch (error) {
                log('音频上下文初始化失败: ' + error.message, 'error');
            }
        }

        function connect() {
            try {
                ws = new WebSocket('ws://localhost:3000');
                
                ws.onopen = function() {
                    isConnected = true;
                    updateStatus('已连接', 'connected');
                    document.getElementById('sendBtn').disabled = false;
                    log('WebSocket连接成功', 'info');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                };
                
                ws.onclose = function() {
                    isConnected = false;
                    updateStatus('连接已断开', 'disconnected');
                    document.getElementById('sendBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = true;
                    log('WebSocket连接关闭', 'info');
                    
                    // 5秒后重连
                    setTimeout(connect, 5000);
                };
                
                ws.onerror = function(error) {
                    log('WebSocket错误: ' + error, 'error');
                };
                
            } catch (error) {
                log('连接失败: ' + error.message, 'error');
                setTimeout(connect, 5000);
            }
        }

        function handleMessage(data) {
            const timestamp = new Date().toLocaleTimeString();
            
            switch(data.type) {
                case 'connected':
                    log(`[${timestamp}] 服务器确认连接: ${data.connectionId}`, 'info');
                    break;
                    
                case 'text_received':
                    log(`[${timestamp}] 📝 服务器收到: ${data.content}`, 'text');
                    stats.startTime = Date.now();
                    stats.chunkCount = 0;
                    stats.audioCount = 0;
                    stats.latencies = [];
                    updateMetrics();
                    break;
                    
                case 'ai_start':
                    log(`[${timestamp}] 🤖 ${data.message}`, 'info');
                    updateStatus('AI正在思考中...', 'processing');
                    break;
                    
                case 'ai_text_stream':
                    // 不记录每个文本流，避免日志过多
                    break;
                    
                case 'immediate_tts_start':
                    stats.chunkCount++;
                    const chunkLatency = Date.now() - stats.startTime;
                    log(`[${timestamp}] 🚀 开始立即TTS [${stats.chunkCount}]: ${data.content}`, 'tts');
                    log(`[${timestamp}] ⏱️ Chunk延迟: ${chunkLatency}ms`, 'info');
                    updateMetrics();
                    break;
                    
                case 'audio_stream':
                    stats.audioCount++;
                    const audioLatency = Date.now() - stats.startTime;
                    stats.latencies.push(audioLatency);
                    log(`[${timestamp}] 🎵 音频流 [${data.metadata.chunkIndex}]: ${data.data.length} bytes`, 'audio');
                    
                    if (autoPlay && audioContext) {
                        playAudioChunk(data.data);
                    }
                    updateMetrics();
                    break;
                    
                case 'immediate_tts_complete':
                    const totalLatency = Date.now() - stats.startTime;
                    log(`[${timestamp}] ✅ 立即TTS完成 [${data.chunkIndex}]: ${data.totalTime}ms`, 'tts');
                    break;
                    
                case 'ai_complete':
                    const totalTime = Date.now() - stats.startTime;
                    log(`[${timestamp}] 🎯 AI回复完成，总chunks: ${data.totalChunks}`, 'info');
                    updateStatus('处理完成', 'connected');
                    document.getElementById('stopBtn').disabled = true;
                    updateMetrics();
                    break;
                    
                case 'immediate_tts_error':
                case 'error':
                    log(`[${timestamp}] ❌ 错误: ${data.error || data.message}`, 'error');
                    break;
                    
                default:
                    log(`[${timestamp}] 未知消息类型: ${data.type}`, 'info');
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !isConnected) return;
            
            document.getElementById('sendBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('发送中...', 'processing');
            
            ws.send(JSON.stringify({
                type: 'chat',
                content: message,
                enableTts: true,
                audioFormat: 'mp3'
            }));
            
            input.value = '';
            setTimeout(() => {
                if (isConnected) {
                    document.getElementById('sendBtn').disabled = false;
                }
            }, 1000);
        }

        function stopAudio() {
            if (isConnected) {
                ws.send(JSON.stringify({
                    type: 'stop_audio',
                    stopAll: true
                }));
                log('发送停止音频请求', 'info');
            }
        }

        function playAudioChunk(base64Data) {
            try {
                const binaryData = atob(base64Data);
                const arrayBuffer = new ArrayBuffer(binaryData.length);
                const uint8Array = new Uint8Array(arrayBuffer);
                
                for (let i = 0; i < binaryData.length; i++) {
                    uint8Array[i] = binaryData.charCodeAt(i);
                }
                
                audioContext.decodeAudioData(arrayBuffer)
                    .then(audioBuffer => {
                        const source = audioContext.createBufferSource();
                        source.buffer = audioBuffer;
                        source.connect(audioContext.destination);
                        source.start();
                    })
                    .catch(error => {
                        log('音频播放失败: ' + error.message, 'error');
                    });
                    
            } catch (error) {
                log('音频解码失败: ' + error.message, 'error');
            }
        }

        function updateStatus(message, className) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + className;
        }

        function updateMetrics() {
            document.getElementById('chunkCount').textContent = stats.chunkCount;
            document.getElementById('audioCount').textContent = stats.audioCount;
            
            if (stats.latencies.length > 0) {
                const avgLatency = stats.latencies.reduce((a, b) => a + b, 0) / stats.latencies.length;
                document.getElementById('avgLatency').textContent = Math.round(avgLatency) + 'ms';
            }
            
            if (stats.startTime) {
                const totalTime = Date.now() - stats.startTime;
                document.getElementById('totalTime').textContent = totalTime + 'ms';
            }
        }

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry log-' + type;
            entry.textContent = message;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            stats = {
                chunkCount: 0,
                audioCount: 0,
                latencies: [],
                startTime: null
            };
            updateMetrics();
        }

        function testConnection() {
            if (isConnected) {
                ws.send(JSON.stringify({
                    type: 'ping',
                    timestamp: Date.now()
                }));
                log('发送ping测试', 'info');
            }
        }

        function toggleAutoPlay() {
            autoPlay = !autoPlay;
            log('自动播放: ' + (autoPlay ? '开启' : '关闭'), 'info');
        }

        // 回车发送
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
